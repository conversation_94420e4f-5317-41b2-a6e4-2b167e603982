<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>魔方还原功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .instructions {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .game-frame {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 5px;
        }
        .controls {
            margin-top: 20px;
            text-align: center;
        }
        .btn {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .btn:hover {
            background: #45a049;
        }
        .btn.danger {
            background: #f44336;
        }
        .btn.danger:hover {
            background: #da190b;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>魔方还原功能测试</h1>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <ol>
                <li>双击魔方开始游戏</li>
                <li>拖拽魔方进行转动操作</li>
                <li>系统会自动记录您的每一步操作</li>
                <li>点击"还原(N)"按钮开始自动还原，N为可还原步数</li>
                <li>还原过程中可以点击按钮暂停/继续</li>
                <li>点击"手动下一步"可以切换到手动模式</li>
                <li>点击"清除"清空操作历史</li>
            </ol>
            <p><strong>注意：</strong>还原功能基于操作历史，包含打乱步骤和您的手动操作步骤。</p>
        </div>
        
        <iframe src="index.html" class="game-frame"></iframe>
        
        <div class="controls">
            <p>魔方还原功能已集成到游戏中，请在游戏界面中查看右下角的按钮。</p>
        </div>
    </div>

    <script>
        // 监听来自iframe的消息
        window.addEventListener('message', function(event) {
            if (event.data.type === 'moveRecorded') {
                console.log('记录到移动:', event.data.move);
            }
        });
    </script>
</body>
</html>
